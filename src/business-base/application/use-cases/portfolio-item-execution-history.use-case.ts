import { v4 as uuid } from 'uuid';
import { PortfolioItemExecutionHistoryDto } from '@business-base/application/dto/in/portfolio-item-execution-history.dto';
import { PortfolioItemExecutionHistoryPort } from '@business-base/infrastructure/ports/db/portfolio-item-execution-history.port';
import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { PortfolioItemStatus } from '@common/enums';

@Injectable()
export class PortfolioItemExecutionHistoryUseCase {
  constructor(
    @Inject('PortfolioItemExecutionHistoryPort')
    private readonly portfolioItemExecutionHistoryAdapter: PortfolioItemExecutionHistoryPort,
  ) {}

  async create(
    createPortfolioItemExecutionHistory: PortfolioItemExecutionHistoryDto,
  ): Promise<PortfolioItemExecutionHistoryDto> {
    const { newStatus, oldStatus, portfolioItemId, reason } = createPortfolioItemExecutionHistory;
    logger.info(`Creating portfolioItemExecutionHistory with portfolioItemId: ${portfolioItemId}`);
    createPortfolioItemExecutionHistory.id = uuid();

    const portfolioItemExecutionHistory = await this.portfolioItemExecutionHistoryAdapter.create({
      id: createPortfolioItemExecutionHistory.id,
      portfolioItemId,
      newStatus,
      oldStatus,
      reason,
    });

    return {
      ...createPortfolioItemExecutionHistory,
      createdAt: portfolioItemExecutionHistory.createdAt,
      updatedAt: portfolioItemExecutionHistory.updatedAt,
      reason: portfolioItemExecutionHistory.reason,
    };
  }

  async findAllByPortfolioItemId(
    portfolioItemId: string,
  ): Promise<PortfolioItemExecutionHistoryDto[]> {
    if (!portfolioItemId) {
      return [];
    }
    logger.info(`Finding all portfolioItemExecutionHistory by portfolioItemId: ${portfolioItemId}`);
    const portfolioItemExecutionHistories = await this.portfolioItemExecutionHistoryAdapter.getAll({
      portfolioItemId,
    });

    return portfolioItemExecutionHistories.map(portfolioItemExecutionHistory => {
      return {
        id: portfolioItemExecutionHistory.id,
        portfolioItemId: portfolioItemExecutionHistory.portfolioItemId,
        newStatus: portfolioItemExecutionHistory.newStatus,
        oldStatus: portfolioItemExecutionHistory.oldStatus,
        createdAt: portfolioItemExecutionHistory.createdAt,
        updatedAt: portfolioItemExecutionHistory.updatedAt,
        reason: portfolioItemExecutionHistory.reason,
        status: portfolioItemExecutionHistory.status,
      };
    });
  }

  async findAllByPortfolioItemIdAndNewStatus(
    portfolioItemId: string,
    newStatus: PortfolioItemStatus,
  ): Promise<PortfolioItemExecutionHistoryDto[]> {
    if (!portfolioItemId) {
      return [];
    }
    logger.info(
      `Finding all portfolioItemExecutionHistory by portfolioItemId: ${portfolioItemId} and newStatus: ${newStatus}`,
    );
    const portfolioItemExecutionHistories = await this.portfolioItemExecutionHistoryAdapter.getAll({
      portfolioItemId,
      newStatus,
    });

    return portfolioItemExecutionHistories.map(portfolioItemExecutionHistory => {
      return {
        id: portfolioItemExecutionHistory.id,
        portfolioItemId: portfolioItemExecutionHistory.portfolioItemId,
        newStatus: portfolioItemExecutionHistory.newStatus,
        oldStatus: portfolioItemExecutionHistory.oldStatus,
        createdAt: portfolioItemExecutionHistory.createdAt,
        updatedAt: portfolioItemExecutionHistory.updatedAt,
        reason: portfolioItemExecutionHistory.reason,
        status: portfolioItemExecutionHistory.status,
      };
    });
  }
}
