import {
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PortfolioItemDto {
  @ApiProperty({
    description: 'The id of the portfolio item',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'The id of the portfolio',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID('4')
  @IsNotEmpty({ message: 'portfolioId is required' })
  readonly portfolioId: string;

  @ApiProperty({
    description: 'The phone number of the portfolio item',
    example: '+5511999999999',
  })
  @IsString()
  @IsNotEmpty({ message: 'phoneNumber is required' })
  @IsPhoneNumber('BR', { message: 'phoneNumber must be a valid phone number' })
  readonly phoneNumber: string;

  @ApiProperty({
    description: 'The customer name of the portfolio item',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty({ message: 'name is required' })
  readonly name: string;

  @ApiProperty({
    description: 'The custom data of the portfolio item',
    example: {
      name: 'John Doe',
      email: '<EMAIL>',
    },
  })
  @IsNotEmpty({ message: 'customData is required' })
  readonly customData: any;

  @ApiProperty({
    description: 'The line of the portfolio item',
    example: 1,
  })
  @IsNumber({ allowNaN: false, allowInfinity: false, maxDecimalPlaces: 0 })
  readonly line: number;

  @ApiProperty({
    description: 'The created at of the portfolio item',
    example: '2021-01-01',
  })
  @IsDate()
  @IsOptional()
  createdAt?: Date;

  @ApiProperty({
    description: 'The updated at of the portfolio item',
    example: '2021-01-01',
  })
  @IsDate()
  @IsOptional()
  updatedAt?: Date;

  constructor(
    portfolioId: string,
    phoneNumber: string,
    name: string,
    customData: any,
    line: number,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.portfolioId = portfolioId;
    this.phoneNumber = phoneNumber;
    this.name = name;
    this.customData = customData;
    this.line = line;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
