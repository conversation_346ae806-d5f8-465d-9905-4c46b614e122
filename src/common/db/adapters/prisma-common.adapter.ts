import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaClient } from '@prisma/client';
import { DbCommonPort, SelectFields } from '@common/db/ports/common.port';
import { RecordStatus } from '@common/enums';

export abstract class PrismaCommonAdapter<T> implements DbCommonPort<T> {
  protected readonly prismaClient: PrismaClient;

  constructor(
    prisma: PrismaService,
    private readonly entity: string,
  ) {
    this.prismaClient = prisma.client;
  }

  async getAll(where?: any, select?: SelectFields<T>): Promise<T[]> {
    if (where?.status === '*') {
      delete where.status; // not to filter by status
    } else {
      where = {
        ...where,
        status: where?.status ?? { not: RecordStatus.DELETED },
      };
    }

    return this.prismaClient[this.entity].findMany({ where, select });
  }

  async count(where?: any): Promise<number> {
    if (where?.status === '*') {
      delete where.status; // not to filter by status
    } else {
      where = {
        ...where,
        status: where?.status ?? { not: RecordStatus.DELETED },
      };
    }

    return this.prismaClient[this.entity].count({ where });
  }

  async create(entity: T): Promise<T> {
    if (!entity['createdAt']) {
      entity['createdAt'] = new Date();
    }
    
    if (!entity['updatedAt']) {
      entity['updatedAt'] = new Date();
    }

    return this.prismaClient[this.entity].create({ data: entity });
  }

  async createMany(entities: T[]): Promise<T[]> {
    if (!Array.isArray(entities)) {
      throw new Error('Entities must be an array');
    }

    entities.forEach(entity => {
      entity['createdAt'] = new Date();
      entity['updatedAt'] = new Date();
    });

    return this.prismaClient[this.entity].createManyAndReturn({
      data: entities,
      skipDuplicates: true,
    });
  }

  async get(id: string): Promise<T> {
    const where = {
      id,
      status: { not: RecordStatus.DELETED },
    };
    return this.prismaClient[this.entity].findUnique({ where });
  }

  async update(entity: T): Promise<T> {
    entity['updatedAt'] = new Date();
    return this.prismaClient[this.entity].update({
      where: { id: entity['id'] },
      data: entity,
    });
  }

  async delete(id: string): Promise<T> {
    return this.prismaClient[this.entity].update({
      where: { id: id },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteMany(where: any): Promise<void> {
    await this.prismaClient[this.entity].update({ where, data: { status: RecordStatus.DELETED } });
  }
}
