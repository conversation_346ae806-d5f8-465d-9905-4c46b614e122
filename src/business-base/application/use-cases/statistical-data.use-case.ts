import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { RecordStatus } from '@common/enums';
import {
  StatsDataSource,
} from '@business-base/application/dto/customer-preferences.dto';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { StatsConfig, StatsFieldConfig } from '@business-base/domain/entities/customer-preferences.entity';

export interface DealValueSummary {
  portfolioId: string;
  portfolioName: string;
  totalDealValue: number;
  itemCount: number;
}

export interface CustomerDealValueSummary {
  customerId: string;
  totalDealValue: number;
  portfolios: DealValueSummary[];
}

@Injectable()
export class StatisticalDataUseCase {
  constructor(
    @Inject('CollectCashStatsPort')
    private readonly collectCashStatsAdapter: CollectCashStatsPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort
  ) { }

  /**
   * Extracts and stores statistical data for a portfolio item based on workflow configuration
   */
  async extractAndStoreStatisticalData(
    customerId: string,
    portfolioItem: PortfolioItemEntity,
    workflowId: string,
    statsConfig: StatsConfig[],
    hasCustomCreatedAt: boolean = false
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Extracting and storing statistical data', {
        traceId,
        customerId,
        portfolioId: portfolioItem.portfolioId,
        portfolioItemId: portfolioItem.id,
        workflowId,
        operation: 'extractAndStoreStatisticalData',
        layer: 'USE_CASE',
      });

      // Check if we have statistical configuration
      if (!statsConfig || statsConfig.length === 0) {
        logger.info('No workflow-specific statistical configuration found', {
          traceId,
          workflowId,
          portfolioId: portfolioItem.portfolioId,
          portfolioItemId: portfolioItem.id,
          operation: 'extractAndStoreStatisticalData',
          layer: 'USE_CASE',
        });
        return;
      }

      for (const config of statsConfig) {
        if (config.workflowId === workflowId) {
          // Extract statistical values
          const extractedData = await this.extractStatisticalValues(config, portfolioItem);

          // Only store if we have valid data (any value > 0 cents)
          if (
            extractedData.dealValueCents > 0 ||
            extractedData.installments > 0 ||
            extractedData.originalDebtCents > 0
          ) {
            const collectCashStats = new CollectCashStatsEntity(
              customerId,
              portfolioItem.portfolioId,
              portfolioItem.id,
              workflowId,
              extractedData.dealValueCents,
              extractedData.originalDebtCents,
              extractedData.installments,
              RecordStatus.ACTIVE,
              hasCustomCreatedAt ? new Date(portfolioItem.createdAt) : undefined,
              hasCustomCreatedAt ? new Date(portfolioItem.updatedAt) : undefined,
            );

            await this.collectCashStatsAdapter.create(collectCashStats);

            logger.info('Statistical data stored successfully', {
              traceId,
              customerId,
              portfolioId: portfolioItem.portfolioId,
              portfolioItemId: portfolioItem.id,
              workflowId,
              dealValueCents: extractedData.dealValueCents,
              originalDebtCents: extractedData.originalDebtCents,
              dealValueReais: (extractedData.dealValueCents / 100).toFixed(2),
              originalDebtReais: (extractedData.originalDebtCents / 100).toFixed(2),
              operation: 'extractAndStoreStatisticalData',
              layer: 'USE_CASE',
            });
          } else {
            logger.info('No valid statistical data to store (all values are 0 cents)', {
              traceId,
              portfolioId: portfolioItem.portfolioId,
              portfolioItemId: portfolioItem.id,
              workflowId,
              dealValueCents: extractedData.dealValueCents,
              originalDebtCents: extractedData.originalDebtCents,
              operation: 'extractAndStoreStatisticalData',
              layer: 'USE_CASE',
            });
          }
        }
      }
    } catch (error) {
      logger.error('Error extracting and storing statistical data', {
        traceId,
        customerId,
        portfolioId: portfolioItem.portfolioId,
        portfolioItemId: portfolioItem.id,
        workflowId,
        error: JSON.stringify(error),
        operation: 'extractAndStoreStatisticalData',
        layer: 'USE_CASE',
      });
      throw error;
    }
  }


  /**
   * Extracts statistical values from a workflow configuration
   * @returns Object with monetary values in integer cents for financial precision
   */
  private async extractStatisticalValues(
    workflowConfig: StatsConfig,
    portfolioItem: PortfolioItemEntity,
  ): Promise<{
    dealValueCents: number;
    originalDebtCents: number;
    installments: number;
  }> {
    const traceId = CorrelationContextService.getTraceId();
    const result = { dealValueCents: 0, originalDebtCents: 0, installments: 1 };

    try {
      // Extract dealValue from dealValue (in cents)
      if (workflowConfig.dealValue) {
        result.dealValueCents = await this.extractFieldValue(
          portfolioItem,
          workflowConfig.dealValue,
          'dealValue',
        );
      }

      // Extract originalDebt (in cents)
      if (workflowConfig.originalDebt) {
        result.originalDebtCents = await this.extractFieldValue(
          portfolioItem,
          workflowConfig.originalDebt,
          'originalDebt',
        );
      }

      // Extract installments
      if (workflowConfig.installments) {
        // Installments default should be 1
        result.installments = await this.extractFieldValue(
          portfolioItem,
          workflowConfig.installments,
          'installments',
        );
        if (!result.installments || result.installments === 0) {
          result.installments = 1;
        }
      }

      logger.info('Statistical values extracted successfully', {
        traceId,
        portfolioItemId: portfolioItem.id,
        dealValueCents: result.dealValueCents,
        originalDebtCents: result.originalDebtCents,
        dealValueReais: (result.dealValueCents / 100).toFixed(2),
        originalDebtReais: (result.originalDebtCents / 100).toFixed(2),
        installments: result.installments,
        operation: 'extractStatisticalValues',
        layer: 'USE_CASE',
      });

      return result;
    } catch (error) {
      logger.error('Error extracting statistical values', {
        traceId,
        portfolioItemId: portfolioItem.id,
        error: JSON.stringify(error),
        operation: 'extractStatisticalValues',
        layer: 'USE_CASE',
      });
      return result; // Return zeros on error
    }
  }

  /**
   * Extracts a single field value from the specified data source
   */
  private async extractFieldValue(
    portfolioItem: PortfolioItemEntity,
    fieldConfig: StatsFieldConfig,
    fieldName: string,
  ): Promise<number> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      let data: any;

      if (fieldConfig.source === StatsDataSource.CUSTOM_DATA) {
        const customData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
          portfolioItem.customDataId,
          portfolioItem.id,
        );
        data = customData?.customData;
      } else if (fieldConfig.source === StatsDataSource.MIDDLEWARE) {
        if (!portfolioItem.middlewareResponseOutputId) {
          logger.info('No middleware response found for portfolio item', {
            traceId,
            portfolioItemId: portfolioItem.id,
            fieldName,
            operation: 'extractFieldValue',
            layer: 'USE_CASE',
          });
          return 0;
        }

        const middlewareResponse = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
          portfolioItem.middlewareResponseOutputId,
          portfolioItem.id,
        );
        data = middlewareResponse?.data;
      }

      if (!data) {
        logger.info('No data found for field extraction', {
          traceId,
          portfolioItemId: portfolioItem.id,
          fieldName,
          source: fieldConfig.source,
          operation: 'extractFieldValue',
          layer: 'USE_CASE',
        });
        return 0;
      }

      // Extract value using the path
      const rawValue = this.getValueFromPath(data, fieldConfig.path);
      if (fieldName === 'installments') {
        //rawValue default should be 1
        if (!rawValue || rawValue === '' || rawValue === 0) {
          return 1;
        }
        return parseInt(rawValue, 10);
      }

      const valueInCents = this.parseNumericValueToCents(rawValue);

      logger.info('Field value extracted successfully', {
        traceId,
        portfolioItemId: portfolioItem.id,
        fieldName,
        source: fieldConfig.source,
        path: fieldConfig.path,
        rawValue,
        valueInCents,
        valueInReais: (valueInCents / 100).toFixed(2),
        operation: 'extractFieldValue',
        layer: 'USE_CASE',
      });

      return valueInCents;
    } catch (error) {
      logger.error('Error extracting field value', {
        traceId,
        portfolioItemId: portfolioItem.id,
        fieldName,
        source: fieldConfig.source,
        path: fieldConfig.path,
        error: JSON.stringify(error),
        operation: 'extractFieldValue',
        layer: 'USE_CASE',
      });
      return 0;
    }
  }

  /**
   * Extracts value from nested object using dot or bracket notation safely
   */
  private getValueFromPath(data: any, path: string): any {
    try {
      if (data == null || typeof path !== 'string') return undefined;

      const segments: string[] = [];
      const regex = /(?:\[['"]?(.*?)['"]?\])|(?:\.?([^.\[\]]+))/g;
      let match;

      while ((match = regex.exec(path))) {
        const key = match[1] ?? match[2];
        if (key !== undefined) segments.push(key);
      }

      return segments.reduce((obj, key) => obj?.[key], data);
    } catch (error) {
      logger.warn('Error parsing path, returning undefined', {
        path,
        error: JSON.stringify(error),
        operation: 'getValueFromPath',
        layer: 'USE_CASE',
      });
      return undefined;
    }
  }

  /**
   * Parses a value to integer cents format for financial precision, handling Brazilian currency format
   * Converts floating-point monetary values to integer cents to avoid precision errors
   * @param value - The value to parse (number, string, or other)
   * @returns Integer value in cents (e.g., 12.34 becomes 1234 cents)
   */
  private parseNumericValueToCents(value: any): number {
    if (value == null || value === '') {
      return 0;
    }

    let numericValue: number;

    if (typeof value === 'number') {
      numericValue = value;
    } else if (typeof value === 'string') {
      // Handle Brazilian currency format (e.g., "2.750,50" or "2,750.50")
      const cleanValue = value.replace(',', '.'); // Replace decimal comma with dot

      numericValue = parseFloat(cleanValue);
      if (isNaN(numericValue)) {
        return 0;
      }
    } else {
      return 0;
    }

    // Convert to integer cents using Math.round for proper rounding
    // This follows the same pattern as portfolio.use-case.ts financial calculations
    return Math.round(numericValue * 100);
  }

  async getTotalDealValueByCustomer(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    return await this.collectCashStatsAdapter.getTotalDealValueByCustomerIdWithDateRange(
      customerId,
      startDate,
      endDate,
    );
  }

  async getAverageTicketByCustomerId(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    return await this.collectCashStatsAdapter.getAverageTicketByCustomerIdWithDateRange(
      customerId,
      startDate,
      endDate,
    );
  }

  async getAverageTicketByPortfolioId(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    return await this.collectCashStatsAdapter.getAverageTicketByPortfolioIdWithDateRange(
      portfolioId,
      startDate,
      endDate,
    );
  }

  async getPortfolioDealValue(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    return await this.collectCashStatsAdapter.getTotalDealValueByPortfolioIdWithDateRange(
      portfolioId,
      startDate,
      endDate,
    );
  }
}
