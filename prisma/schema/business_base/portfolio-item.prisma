model portfolioItem {
  id                          String    @id @default(uuid()) @db.Uuid
  portfolioId                 String    @map(name: "portfolio_id") @db.Uuid
  currentStatus               String?   @map(name: "current_status")
  phoneNumber                 String    @map(name: "phone_number")
  name                        String?   @map(name: "name")
  customDataId                String    @map(name: "custom_data_id") @db.Uuid
  middlewareResponseOutputId  String?   @map(name: "middleware_response_output_id") @db.Uuid
  line                        BigInt?
  lastInteraction             DateTime? @map(name: "last_interaction")
  lastMessageSentAt           DateTime? @map(name: "last_message_sent_at")
  lastFollowUpAt              DateTime? @map(name: "last_follow_up_at")
  waitingBusinessUserResponse Boolean   @default(false) @map(name: "waiting_business_user_response")
  followUpCount               Int       @default(0) @map(name: "follow_up_count")
  status                      String    @default("ACTIVE")
  createdAt                   DateTime  @default(now()) @map(name: "created_at")
  updatedAt                   DateTime  @updatedAt @map(name: "updated_at")

  @@index([id, portfolioId])
  @@map(name: "portfolio_item")
  @@schema("business_base")
}
